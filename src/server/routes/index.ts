/**
 * 路由入口文件
 * 整合所有 API 路由
 */

import { Router } from 'express';
import { createModelsRouter } from './models';
import { createChatRouter } from './chat';

/**
 * 创建主路由
 * 整合所有子路由
 * 
 * @returns Express 路由实例
 */
export function createApiRouter(): Router {
  const router = Router();

  // 模型相关路由
  router.use('/models', createModelsRouter());

  // 聊天相关路由
  router.use('/chat', createChatRouter());

  // 健康检查端点
  router.get('/health', (_req, res) => {
    res.json({
      success: true,
      data: {
        status: 'healthy',
        timestamp: Date.now(),
        version: process.env['npm_package_version'] || '1.0.0'
      }
    });
  });

  // API 信息端点
  router.get('/info', (_req, res) => {
    res.json({
      success: true,
      data: {
        name: '当贝AI Provider API',
        version: process.env['npm_package_version'] || '1.0.0',
        description: '基于当贝AI的标准HTTP API接口',
        endpoints: {
          models: {
            'GET /api/models': '获取所有支持的模型列表',
            'GET /api/models/recommended': '获取推荐模型列表',
            'GET /api/models/:modelId': '获取特定模型信息',
            'POST /api/models/reload': '重新加载模型数据'
          },
          chat: {
            'POST /api/chat': '聊天对话接口（支持流式和非流式）'
          },
          system: {
            'GET /api/health': '健康检查',
            'GET /api/info': 'API信息'
          }
        },
        timestamp: Date.now()
      }
    });
  });

  return router;
}
